// 系统配置相关API
const db = wx.cloud.database()

/**
 * 获取系统配置
 * @param {string} category - 配置分类 (general/ads/search/download)
 * @returns {Promise<Object>} 配置对象
 */
async function getSystemConfigs(category = 'general') {
  try {
    const result = await db.collection('system_configs')
      .where({
        category: category
      })
      .get()

    if (result.data && result.data.length > 0) {
      const configs = {}
      result.data.forEach(item => {
        // 根据类型转换值
        let value = item.value
        if (item.type === 'boolean') {
          value = value === 'true' || value === true
        } else if (item.type === 'number') {
          value = Number(value)
        } else if (item.type === 'json') {
          try {
            value = JSON.parse(value)
          } catch (e) {
            console.warn('JSON解析失败:', item.key, value)
          }
        }
        configs[item.key] = value
      })
      return configs
    }
    return {}
  } catch (error) {
    console.error('获取系统配置失败:', error)
    throw error
  }
}

/**
 * 获取单个配置项
 * @param {string} key - 配置键名
 * @returns {Promise<any>} 配置值
 */
async function getSystemConfig(key) {
  try {
    const result = await db.collection('system_configs')
      .where({
        key: key
      })
      .limit(1)
      .get()

    if (result.data && result.data.length > 0) {
      const item = result.data[0]
      let value = item.value
      
      // 根据类型转换值
      if (item.type === 'boolean') {
        value = value === 'true' || value === true
      } else if (item.type === 'number') {
        value = Number(value)
      } else if (item.type === 'json') {
        try {
          value = JSON.parse(value)
        } catch (e) {
          console.warn('JSON解析失败:', key, value)
        }
      }
      return value
    }
    return null
  } catch (error) {
    console.error('获取系统配置失败:', error)
    throw error
  }
}

/**
 * 获取应用基本信息
 * @returns {Promise<Object>} 应用信息对象
 */
async function getAppInfo() {
  try {
    const configs = await getSystemConfigs('general')
    return {
      name: configs.app_name || 'K12教育资源小程序',
      version: configs.app_version || '1.0.0',
      contactInfo: configs.contact_info || '如有问题请联系客服'
    }
  } catch (error) {
    console.error('获取应用信息失败:', error)
    return {
      name: 'K12教育资源小程序',
      version: '1.0.0',
      contactInfo: '如有问题请联系客服'
    }
  }
}



/**
 * 获取搜索配置
 * @returns {Promise<Object>} 搜索配置对象
 */
async function getSearchConfigs() {
  try {
    const configs = await getSystemConfigs('search')
    return {
      hotKeywords: configs.hot_keywords ? configs.hot_keywords.split(',') : [],
      hotKeywordsEnabled: configs.hot_keywords_enabled || false,
      hotKeywordsMaxCount: configs.hot_keywords_max_count || 10
    }
  } catch (error) {
    console.error('获取搜索配置失败:', error)
    return {
      hotKeywords: [],
      hotKeywordsEnabled: false,
      hotKeywordsMaxCount: 10
    }
  }
}

module.exports = {
  getSystemConfigs,
  getSystemConfig,
  getAppInfo,
  getSearchConfigs
}