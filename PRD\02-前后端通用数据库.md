# K12教育资料小程序数据库设计文档

## 1. 数据库概述

### 1.1 数据库类型
- **前端（小程序）**: 微信云数据库（基于MongoDB的NoSQL数据库）
- **后端（管理系统）**: 本地数据库（可使用SQLite或MySQL）

### 1.2 设计原则
- 极简化设计，专注核心功能
- 支持4维分类体系（年级、学科、学期、类别）
- 激励广告换取下载权限
- 系统配置灵活可调
- 分类筛选基于files集合动态统计，确保数据实时性

### 1.3 数据表总览
本数据库设计包含3个核心数据表：
1. 文件表 (files) 
2. 系统配置表 (system_configs)
3. 意见反馈表 (feedback)

## 2. 数据表详细设计

### 2.1 文件表 (files)

**表说明**: 存储教育资料文件的详细信息，同时作为分类筛选的数据源

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 文件唯一标识 |
| title | String | 是 | - | 文件标题 |
| description | String | 否 | "" | 文件描述 |
| file_url | String | 是 | - | 文件存储URL |
| file_type | String | 是 | - | 文件类型：pdf/doc/ppt/xls/zip等 |
| file_size | Number | 是 | - | 文件大小（字节） |
| preview_images | Array | 否 | [] | 资料预览图数组，最多3张，格式：[{url: "图片URL", order: 1}] |
| category | String | 是 | "regular" | 资料分类：regular(常规资料)/upgrade(升学专区) |
| grade | String | 否 | null | 年级：幼升小/一年级/二年级/三年级/四年级/五年级/六年级/小升初 |
| subject | String | 是 | - | 学科：语文/数学/英语/科学/音乐/美术/体育 |
| volume | String | 是 | - | 册别：上册/下册/全册 |
| section | String | 是 | - | 板块：单元同步/专项练习/拼音启蒙/认识数字/习惯养成/学科启蒙/知识科普/语文冲刺/数学冲刺/英语强化/真题模拟/面试准备 |
| tags | Array | 否 | [] | 文件标签数组，用于标记文件的特殊属性或分类，如：["重点推荐","热门下载","新上传","期末复习","奥数竞赛"] |
| ad_required_count | Number | 是 | 1 | 下载前需要观看的广告次数（可基于文件类型、大小等动态计算，也可手动设置） |
| uploader_id | String | 否 | "system" | 上传者ID |
| created_time | Date | 是 | 当前时间 | 创建时间 |
| status | String | 是 | "active" | 文件状态：active/inactive/deleted |
| sort_order | Number | 是 | 0 | 排序权重，数值越大越靠前，0表示默认排序 |
| pages | Number | 否 | null | 文件总页数 |
| features | Array | 否 | [] | 文件特性标签数组：["高清版","可打印","含答案","有解析"] |
| download_count | Number | 是 | 0 | 下载次数统计 |
| view_count | Number | 是 | 0 | 查看次数统计 |
| share_count | Number | 是 | 0 | 分享次数统计 |

**索引设计**:
- 主键索引：_id
- 复合索引：category + grade + subject + volume + section
- 普通索引：status, sort_order, created_time, pages, download_count, view_count, share_count
- 数组索引：features, tags

**分类筛选说明**:
- **常规资料筛选** (category="regular")：
  - 年级筛选：基于grade字段动态统计（一年级/二年级/三年级/四年级/五年级/六年级）
  - 学科筛选：基于subject字段动态统计，按"语文、数学、英语"优先排序
  - 册别筛选：基于volume字段动态统计（上册/下册/全册）
  - 板块筛选：基于section字段动态统计
- **升学专区筛选** (category="upgrade")：
  - 升学类型筛选：基于grade字段动态统计（幼升小/小升初）
  - 学科筛选：基于subject字段动态统计
  - 专区分类筛选：基于section字段动态统计

**常规资料板块分类标准值**:
- 单元同步
- 单元知识点  
- 核心知识点
- 试卷
- 专项练习

**升学专区分类标准值**:
- **幼升小专区**: 拼音启蒙、认识数字、习惯养成、学科启蒙、知识科普
- **小升初专区**: 语文冲刺、数学冲刺、英语强化、真题模拟、面试准备

> 注：板块分类可在后台上传文件时自定义，上述为标准推荐值

**数据示例**:
```javascript
// 常规资料示例
{
  _id: "file001",
  title: "一年级语文上册第一单元练习题",
  category: "regular",
  grade: "一年级",
  subject: "语文", 
  volume: "上册",
  section: "单元同步",
  sort_order: 100,
  pages: 8,
  features: ["高清版", "含答案"],
  ad_required_count: 1,
  download_count: 156,
  view_count: 892,
  share_count: 23
}

// 升学专区资料示例
{
  _id: "file002", 
  title: "幼升小拼音启蒙练习册",
  category: "upgrade",
  grade: "幼升小",
  subject: "语文",
  volume: "全册",
  section: "拼音启蒙",
  sort_order: 500,
  pages: 20,
  features: ["高清版", "可打印", "含答案"],
  ad_required_count: 1,
  download_count: 89,
  view_count: 456,
  share_count: 12
}
```

**排序逻辑**:
```javascript
// 推荐的查询排序
db.collection('files')
  .orderBy('sort_order', 'desc')    // 排序权重优先
  .orderBy('created_time', 'desc')  // 同权重按时间排序
  .get()
```

**前端显示逻辑**:
```javascript
// 生成文件信息显示文本
function generateFileInfo(file) {
  const parts = [];
  
  // 文件类型（从file_type推导）
  parts.push(file.file_type.toUpperCase());
  
  // 页数信息
  if (file.pages) {
    parts.push(`${file.pages}页`);
  }
  
  // 特性信息（最多显示2个）
  if (file.features && file.features.length > 0) {
    parts.push(...file.features.slice(0, 2));
  }
  
  return parts.join('·');  // "PDF·20页·可打印"
}

// 根据排序权重显示标识
function getDisplayLabel(sortOrder) {
  if (sortOrder >= 1000) return '置顶';
  if (sortOrder >= 500) return '热门';
  if (sortOrder >= 100) return '推荐';
  return '';
}
```

**字段优化说明**:
- ✅ 删除了冗余的`upgradeType`和`upgradeCategory`字段，整合到volume和section
- ✅ 删除了`upload_time`和`updated_time`字段，统一使用`created_time`
- ✅ 删除了`points`字段，与广告驱动模式不符
- ✅ 删除了`featured_priority`字段，功能与sort_order重叠
- ✅ 删除了`fileInfo`嵌套对象，拆分为独立的pages和features字段
- ✅ 新增`category`字段统一区分常规资料和升学专区
- ✅ 新增`ad_required_count`字段支持灵活的广告次数控制
- ✅ 保留`download_count`、`view_count`、`share_count`统计字段，支持数据分析
- ✅ 统一使用volume和section字段存储所有分类信息
- ✅ 扁平化数据结构，提升查询性能和索引效率

### 2.2 系统配置表 (system_configs)

**表说明**: 存储系统的各项配置参数，包括广告位配置和系统设置

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 配置唯一标识 |
| key | String | 是 | - | 配置键名 |
| value | String | 是 | - | 配置值 |
| type | String | 是 | "string" | 数据类型：string/number/boolean/json |
| category | String | 是 | "general" | 配置分类：general/ads/search/download |
| description | String | 否 | "" | 配置描述 |
| created_time | Date | 是 | 当前时间 | 创建时间 |



**搜索功能配置**:
| key | value | type | category | description |
|-----|-------|------|----------|-------------|
| hot_keywords | "单元,知识点,练习,试卷" | string | search | 热搜关键词（逗号分隔） |
| hot_keywords_enabled | "true" | boolean | search | 是否启用热搜关键词功能 |
| hot_keywords_max_count | "10" | number | search | 热搜关键词最大显示数量 |




## 3. 广告系统业务逻辑

### 3.1 广告次数校验规则

**仅适用于激励视频广告**（下载按钮广告）：
- 需要校验files表中的`ad_required_count`字段
- 用户必须观看完整的激励视频广告指定次数后才能下载

**其他广告位**（首页底部、详情页中间、列表插入、搜索顶部）：
- 直接展示，无需次数校验
- 主要用于品牌曝光和收益

**激励视频广告次数计算逻辑**：
```javascript
function getRequiredAdCount(file) {
  // 获取该文件需要观看的广告次数
  return file.ad_required_count || 1; // 默认1次
}

// 下载按钮点击处理
function handleDownloadClick(file) {
  const requiredCount = getRequiredAdCount(file);
  let watchedCount = 0;
  
  function showRewardVideo() {
    // 加载激励视频广告
    wx.createRewardedVideoAd({
      adUnitId: getAdConfig('ad_download_button').ad_id
    }).onClose((res) => {
      if (res && res.isEnded) {
        watchedCount++;
        if (watchedCount >= requiredCount) {
          // 观看次数达到要求，开始下载
          startDownload(file);
        } else {
          // 还需要继续观看
          showRewardVideo();
        }
      } else {
        // 用户中途关闭广告，重新播放
        showRewardVideo();
      }
    });
  }
  
  showRewardVideo();
}
```

### 3.2 广告位配置说明

**广告位类型**：
- **首页底部** (`ad_home_bottom`)：横幅广告，展示在首页底部
- **详情页中间** (`ad_detail_middle`)：原生广告，嵌入在资料详情中间
- **下载按钮** (`ad_download_button`)：激励视频广告，点击下载时播放
- **列表页插入** (`ad_list_insert`)：原生广告，插入在资料列表中
- **搜索页顶部** (`ad_search_top`)：横幅广告，展示在搜索结果顶部

**广告类型说明**：
- **banner**：横幅广告，适合页面顶部/底部
- **native**：原生广告，与内容融合度高
- **reward_video**：激励视频广告，用户观看完整视频获得奖励
- **interstitial**：插屏广告，全屏展示

**配置参数说明**：
- `enabled`：是否启用该广告位
- `provider`：广告商（tencent/bytedance/baidu等）
- `ad_id`：广告位ID，由广告商提供
- `ad_type`：广告形式类型

### 3.3 广告展示流程

**下载按钮激励视频流程**：
```javascript
// 下载按钮广告流程
const downloadAdFlow = {
  1: "用户点击下载按钮",
  2: "检查ad_download_button配置是否启用", 
  3: "加载对应广告商的激励视频广告",
  4: "用户观看完整视频",
  5: "广告完成回调，开始文件下载",
  6: "更新download_count统计"
}
```

**页面广告展示流程**：
```javascript
// 页面广告展示
function showPageAd(adPosition) {
  // 获取对应广告位配置
  const adConfig = getAdConfig(adPosition); // ad_home_bottom, ad_detail_middle等
  
  if (!adConfig.enabled) {
    return; // 广告位未启用
  }
  
  // 根据广告商和类型加载广告
  loadAd({
    provider: adConfig.provider,
    adId: adConfig.ad_id,
    adType: adConfig.ad_type
  });
}
```

**广告配置获取示例**：
```javascript
// 从系统配置获取广告位配置
async function getAdConfig(position) {
  const config = await db.collection('system_configs')
    .where({ key: position })
    .get();
    
  return JSON.parse(config.data[0].value);
}
```

### 2.3 意见反馈表 (feedback)

**表说明**: 存储用户提交的意见反馈信息

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 反馈唯一标识 |
| feedback_type | String | 是 | - | 反馈类型：功能异常/功能建议/内容问题/其他问题 |
| feedback_title | String | 是 | - | 反馈标题 |
| feedback_content | String | 是 | - | 详细描述内容 |
| user_email | String | 否 | "" | 用户邮箱地址（选填） |
| create_time | Date | 是 | 当前时间 | 创建时间 |
| feedback_status | String | 是 | "待处理" | 处理状态：待处理/处理中/已解决/已关闭 |

**索引设计**:
- 主键索引：_id
- 普通索引：feedback_type, feedback_status, create_time

**权限配置**:
- **安全规则**: ADMINWRITE（用户可写入，管理员可读写）

**数据示例**:
```javascript
{
  _id: "feedback001",
  feedback_type: "功能异常",
  feedback_title: "下载按钮点击无反应",
  feedback_content: "在详情页点击下载按钮后，没有任何反应，无法下载文件。",
  user_email: "<EMAIL>",
  create_time: new Date("2025-08-14T09:30:00.000Z"),
  feedback_status: "待处理"
}
```

## 4. 数据库集合权限配置

### 4.1 集合权限总览

| 集合名称 | 权限规则 | 说明 |
|----------|----------|------|
| files | READONLY | 用户只读，管理员可写 |
| system_configs | READONLY | 用户只读，管理员可写 |
| feedback | ADMINWRITE | 用户可写入，管理员可读写 |

### 4.2 权限规则说明

**READONLY规则**:
- 适用于：files、system_configs
- 用户权限：只能读取数据
- 管理员权限：完全读写权限
- 使用场景：展示数据、配置读取

**ADMINWRITE规则**:
- 适用于：feedback
- 用户权限：只能写入数据，无法读取
- 管理员权限：完全读写权限
- 使用场景：用户反馈提交

## 5. 最终优化成果

- 数据表从2个扩展到3个，增加用户反馈功能
- 完全消除冗余字段和嵌套对象
- 统一的4维分类体系
- 支持灵活的广告位配置和多广告商
- 保留关键统计字段，支持数据分析
- 简化的广告配置方案，易于管理和维护
- 完善的用户反馈系统，支持问题跟踪和用户服务
- 合理的权限配置，保障数据安全
