// 文件列表页面逻辑
Page({
  data: {
    // 页面基本信息
    pageTitle: '',
    grade: '',
    category: '',
    
    // 筛选条件
    selectedSubject: '',
    selectedVolume: '',
    selectedType: '',
    selectedSort: '下载量',
    sortOrder: 'desc',
    selectedFilters: [],
    hasSelectedFilters: false,
    
    // 筛选弹窗
    showFilterModal: false,
    currentFilterType: '',
    currentFilterTitle: '',
    currentFilterOptions: [],
    
    // 文件列表
    materialList: [],
    totalCount: 0,
    loading: false,
    page: 1,
    hasMore: true,
    
    // 筛选选项配置
    subjectOptions: [],
    volumeOptions: [],
    typeOptions: [],
    
    sortOptions: [
      { value: 'latest', text: '最新' },
      { value: 'popular', text: '最热' },
      { value: 'download', text: '下载量' },
      { value: 'price_low', text: '积分从低到高' },
      { value: 'price_high', text: '积分从高到低' }
    ]
  },

  onLoad(options) {
    const { grade, title } = options
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: title || '文件列表'
    })
    
    this.setData({
      grade: grade || '',
      pageTitle: title || '文件列表'
    })
    
    console.log('文件列表页面加载，年级参数:', grade)
    
    this.loadFilterOptions().then(() => {
      this.initFilters(options)
      this.loadFilesList(true)
    })
  },

  onPullDownRefresh() {
    this.loadFilesList(true)
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadFilesList()
    }
  },

  // 动态加载筛选选项
  async loadFilterOptions() {
    try {
      const { cloudApi } = require('../../utils/cloudApi')
      const result = await cloudApi.getFilterOptions(this.data.grade)
      
      if (result && result.success) {
        const data = result.data || {}
        
        this.setData({
          subjectOptions: [
            { value: '', text: '全部科目' },
            ...(data.subjects || []).map(subject => ({ 
              value: subject, 
              text: subject 
            }))
          ],
          volumeOptions: [
            { value: '', text: '全部册别' },
            ...(data.volumes || []).map(volume => ({ 
              value: volume, 
              text: volume 
            }))
          ],
          typeOptions: [
            { value: '', text: '全部板块' },
            ...(data.sections || []).map(section => ({ 
              value: section, 
              text: section 
            }))
          ]
        })
      }
      
    } catch (error) {
      console.error('加载筛选选项失败:', error)
      // 使用默认选项
      this.setData({
        subjectOptions: [
          { value: '', text: '全部科目' },
          { value: '语文', text: '语文' },
          { value: '数学', text: '数学' },
          { value: '英语', text: '英语' },
          { value: '科学', text: '科学' },
          { value: '道德与法治', text: '道德与法治' }
        ],
        volumeOptions: [
          { value: '', text: '全部册别' },
          { value: '上册', text: '上册' },
          { value: '下册', text: '下册' },
          { value: '全册', text: '全册' }
        ],
        typeOptions: [
          { value: '', text: '全部板块' },
          { value: '单元同步', text: '单元同步' },
          { value: '单元知识点', text: '单元知识点' },
          { value: '期中试卷', text: '期中试卷' },
          { value: '期末试卷', text: '期末试卷' },
          { value: '练习册', text: '练习册' },
          { value: '课件资料', text: '课件资料' }
        ]
      })
    }
  },

  // 初始化筛选条件
  initFilters(options) {
    const { grade, subject, volume, section } = options
    let selectedFilters = []
    
    // 年级筛选（硬编码传入）
    if (grade) {
      selectedFilters.push({
        type: 'grade',
        text: grade,
        value: grade
      })
    }
    
    // 科目筛选
    if (subject) {
      this.setData({ selectedSubject: subject })
      selectedFilters.push({
        type: 'subject',
        text: subject,
        value: subject
      })
    }
    
    // 册别筛选
    if (volume) {
      this.setData({ selectedVolume: volume })
      selectedFilters.push({
        type: 'volume',
        text: volume,
        value: volume
      })
    }
    
    // 板块筛选
    if (section) {
      this.setData({ selectedType: section })
      selectedFilters.push({
        type: 'section',
        text: section,
        value: section
      })
    }
    
    this.setData({
      selectedFilters,
      hasSelectedFilters: selectedFilters.length > 0
    })
  },

  // 显示筛选弹窗
  showSubjectFilter() {
    this.showFilterModal('subject', '选择科目', this.data.subjectOptions)
  },

  showVolumeFilter() {
    this.showFilterModal('volume', '选择册别', this.data.volumeOptions)
  },

  showTypeFilter() {
    this.showFilterModal('section', '选择板块', this.data.typeOptions)
  },

  // 通用显示筛选弹窗方法
  showFilterModal(type, title, options) {
    const currentValue = this.getCurrentFilterValue(type)
    const processedOptions = options.map(item => ({
      ...item,
      selected: item.value === currentValue
    }))
    
    this.setData({
      showFilterModal: true,
      currentFilterType: type,
      currentFilterTitle: title,
      currentFilterOptions: processedOptions
    })
  },

  // 获取当前筛选值
  getCurrentFilterValue(type) {
    const filter = this.data.selectedFilters.find(item => item.type === type)
    return filter ? filter.value : ''
  },

  // 选择筛选选项
  selectFilterOption(e) {
    const { value, text } = e.currentTarget.dataset
    const { currentFilterType } = this.data
    
    const updateData = {}
    
    // 正确映射筛选器字段
    if (currentFilterType === 'subject') {
      updateData.selectedSubject = value ? text : ''
    } else if (currentFilterType === 'volume') {
      updateData.selectedVolume = value ? text : ''
    } else if (currentFilterType === 'section') {
      updateData.selectedType = value ? text : ''
    }
    
    let selectedFilters = this.data.selectedFilters.filter(item => item.type !== currentFilterType)
    if (value) {
      selectedFilters.push({
        type: currentFilterType,
        text: text,
        value: value
      })
    }
    
    updateData.selectedFilters = selectedFilters
    updateData.hasSelectedFilters = selectedFilters.length > 0
    updateData.showFilterModal = false
    
    this.setData(updateData)
    this.loadFilesList(true)
  },

  // 排序相关方法
  selectSort(e) {
    const { sort } = e.currentTarget.dataset
    
    if (sort === this.data.selectedSort) {
      const newOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
      this.setData({ sortOrder: newOrder })
    } else {
      this.setData({
        selectedSort: sort,
        sortOrder: sort === '积分' ? 'asc' : 'desc'
      })
    }
    
    this.loadFilesList(true)
  },

  toggleSortOrder(e) {
    e.stopPropagation()
    const newOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    this.setData({ sortOrder: newOrder })
    this.loadFilesList(true)
  },

  // 移除筛选条件
  removeFilter(e) {
    const { type } = e.currentTarget.dataset
    let selectedFilters = this.data.selectedFilters.filter(item => item.type !== type)
    
    const updateData = {
      selectedFilters,
      hasSelectedFilters: selectedFilters.length > 0
    }
    
    switch (type) {
      case 'subject':
        updateData.selectedSubject = ''
        break
      case 'volume':
        updateData.selectedVolume = ''
        break
      case 'section':
        updateData.selectedType = ''
        break
      case 'sort':
        updateData.selectedSort = '下载量'
        updateData.sortOrder = 'desc'
        break
    }
    
    this.setData(updateData)
    this.loadFilesList(true)
  },

  // 清空所有筛选条件
  clearAllFilters() {
    this.setData({
      selectedSubject: '',
      selectedVolume: '',
      selectedType: '',
      selectedSort: '下载量',
      sortOrder: 'desc',
      selectedFilters: [],
      hasSelectedFilters: false
    })
    this.loadFilesList(true)
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({ showFilterModal: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 加载文件列表
  async loadFilesList(reset = false) {
    if (this.data.loading) return
    
    const page = reset ? 1 : this.data.page
    
    this.setData({
      loading: true,
      page: page
    })
    
    try {
      const { cloudApi } = require('../../utils/cloudApi')
      const params = this.buildRequestParams()
      params.page = page
      params.pageSize = 20
      
      const result = await cloudApi.getFilesList(params)
      
      if (result && result.success) {
        const processedData = (result.data || []).map(item => 
          this.processItemData(item)
        )
        
        let materialList = reset ? processedData : [...this.data.materialList, ...processedData]
        
        this.setData({
          materialList,
          totalCount: result.total || 0,
          loading: false,
          hasMore: result.hasMore !== false,
          page: page + 1
        })
      } else {
        throw new Error('获取数据失败')
      }
      
    } catch (error) {
      console.error('加载文件列表失败:', error)
      
      this.setData({
        loading: false,
        hasMore: false
      })
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      })
    }
  },

  // 处理数据项
  processItemData(item) {
    return {
      id: item._id,
      title: item.title,
      tags: Array.isArray(item.tags) ? item.tags.slice(0, 4) : [],
      downloadCount: item.download_count || 0,
      viewCount: item.view_count || 0,
      points: item.ad_required_count || 1,
      fileInfo: {
        type: item.file_type || 'PDF',
        pages: item.pages || 0,
        size: this.formatFileSize(item.file_size),
        features: item.features || []
      },
      subject: item.subject,
      grade: item.grade,
      volume: item.volume,
      section: item.section
    }
  },

  // 格式化文件大小
  formatFileSize(size) {
    if (!size || size <= 0) return ''
    if (size > 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
    if (size > 1024) return (size / 1024).toFixed(1) + 'KB'
    return size + 'B'
  },

  // 构建请求参数
  buildRequestParams() {
    const params = {}
    
    // 硬编码年级参数 - 核心筛选条件
    if (this.data.grade) {
      params.grade = this.data.grade
    }
    
    // 添加其他筛选条件
    if (this.data.selectedSubject) {
      params.subject = this.data.selectedSubject
    }
    
    if (this.data.selectedVolume) {
      params.volume = this.data.selectedVolume
    }
    
    if (this.data.selectedType) {
      params.section = this.data.selectedType
    }
    
    // 添加排序参数
    const sortType = this.data.selectedSort
    const sortOrder = this.data.sortOrder
    
    if (sortType === '下载量') {
      params.sortBy = 'download_count'
      params.sortOrder = sortOrder
    } else if (sortType === '更新时间') {
      params.sortBy = 'created_time'
      params.sortOrder = sortOrder
    } else {
      // 默认按推荐排序（管理员权重）
      params.sortBy = 'sort_order'
      params.sortOrder = 'desc'
    }
    
    return params
  },

  // 页面跳转方法
  goBack() {
    wx.navigateBack()
  },

  goToSearch() {
    wx.switchTab({
      url: '/pages/search/search'
    })
  },

  goToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/files-detail/files-detail?id=${id}`
    })
  }
})