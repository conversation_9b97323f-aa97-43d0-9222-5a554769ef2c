<view class="container">
  <!-- 模式切换器 -->
  <view class="mode-switcher">
    <view class="mode-tab {{currentMode === 'normal' ? 'active' : ''}}" 
          bindtap="switchMode" data-mode="normal">
      <text>常规资料</text>
    </view>
    <view class="mode-tab {{currentMode === 'upgrade' ? 'active' : ''}}" 
          bindtap="switchMode" data-mode="upgrade">
      <text>升学专区</text>
    </view>
  </view>

  <!-- 筛选器区域 -->
  <view class="filter-section">
    <!-- 常规模式筛选器 -->
    <block wx:if="{{currentMode === 'normal'}}">
      <view class="filter-row">
        <view class="filter-item {{normalFilters.selectedGrade ? 'selected' : ''}}" bindtap="showGradeFilter">
          <text class="filter-text">{{normalFilters.selectedGrade || '年级'}}</text>
          <text class="filter-arrow {{showGradePopup ? 'rotate' : ''}}">▼</text>
        </view>
        <view class="filter-item {{normalFilters.selectedSubject ? 'selected' : ''}}" bindtap="showSubjectFilter">
          <text class="filter-text">{{normalFilters.selectedSubject || '科目'}}</text>
          <text class="filter-arrow {{showSubjectPopup ? 'rotate' : ''}}">▼</text>
        </view>
      </view>
      <view class="filter-row">
        <view class="filter-item {{normalFilters.selectedVolume ? 'selected' : ''}}" bindtap="showVolumeFilter">
          <text class="filter-text">{{normalFilters.selectedVolume || '册别'}}</text>
          <text class="filter-arrow {{showVolumePopup ? 'rotate' : ''}}">▼</text>
        </view>
        <view class="filter-item {{normalFilters.selectedSection ? 'selected' : ''}}" bindtap="showSectionFilter">
          <text class="filter-text">{{normalFilters.selectedSection || '板块'}}</text>
          <text class="filter-arrow {{showSectionPopup ? 'rotate' : ''}}">▼</text>
        </view>
      </view>
      <view class="filter-actions" wx:if="{{normalFilters.selectedGrade || normalFilters.selectedSubject || normalFilters.selectedVolume || normalFilters.selectedSection}}">
        <view class="reset-btn" bindtap="resetFilters">重置筛选</view>
      </view>
    </block>

    <!-- 专区模式筛选器 -->
    <block wx:else>
      <view class="filter-row">
        <view class="filter-item {{upgradeFilters.selectedUpgradeType ? 'selected' : ''}}" bindtap="showUpgradeTypeFilter">
          <text class="filter-text">{{upgradeFilters.selectedUpgradeType || '专区'}}</text>
          <text class="filter-arrow {{showUpgradeTypePopup ? 'rotate' : ''}}">▼</text>
        </view>
        <view class="filter-item {{upgradeFilters.selectedCategory ? 'selected' : ''}}" bindtap="showUpgradeCategoryFilter">
          <text class="filter-text">{{upgradeFilters.selectedCategory || '板块'}}</text>
          <text class="filter-arrow {{showUpgradeCategoryPopup ? 'rotate' : ''}}">▼</text>
        </view>
      </view>
      <view class="filter-actions" wx:if="{{upgradeFilters.selectedUpgradeType || upgradeFilters.selectedCategory}}">
        <view class="reset-btn" bindtap="resetUpgradeFilters">重置筛选</view>
      </view>
    </block>
  </view>

  <!-- 排序选项 -->
  <view class="sort-section">
    <view class="sort-item {{sortType === 'download_count' ? 'active' : ''}}" bindtap="setSortType" data-type="download">
      <text>下载量</text>
      <text class="sort-arrow" wx:if="{{sortType === 'download_count'}}">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
    </view>
    <view class="sort-item {{sortType === 'created_time' ? 'active' : ''}}" bindtap="setSortType" data-type="time">
      <text>更新时间</text>
      <text class="sort-arrow" wx:if="{{sortType === 'created_time'}}">{{sortOrder === 'asc' ? '↑' : '↓'}}</text>
    </view>
  </view>

  <!-- 资料列表 - 四行布局 -->
  <view class="material-list">
    <view class="material-item" wx:for="{{filesList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <!-- 第一行：标题 -->
      <view class="title-row">
        <view class="material-title">{{item.title}}</view>
      </view>
      
      <!-- 第二行：文件信息行（图标 + 页数 + 大小 + 特征） -->
      <view class="file-info-row">
        <image class="file-icon" wx:if="{{item.fileInfo.type === 'PDF'}}" src="/images/files/icon_pdf.svg" mode="aspectFit" />
        <image class="file-icon" wx:elif="{{item.fileInfo.type === 'DOC' || item.fileInfo.type === 'DOCX'}}" src="/images/files/icon_doc.svg" mode="aspectFit" />
        <image class="file-icon" wx:elif="{{item.fileInfo.type === 'XLS' || item.fileInfo.type === 'XLSX'}}" src="/images/files/icon_xls.svg" mode="aspectFit" />
        <image class="file-icon" wx:elif="{{item.fileInfo.type === 'PPT' || item.fileInfo.type === 'PPTX'}}" src="/images/files/icon_ppt.svg" mode="aspectFit" />
        <image class="file-icon" wx:else src="/images/files/icon_default.svg" mode="aspectFit" />
        
        <text class="file-pages" wx:if="{{item.fileInfo.pages}}">{{item.fileInfo.pages}}页</text>
        <text class="file-size" wx:if="{{item.fileInfo.size}}">{{item.fileInfo.size}}</text>
        
        <view class="feature-tags-inline">
          <text class="feature-tag-small" wx:for="{{item.fileInfo.features}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
      
      <!-- 第三行：标签 -->
      <view class="tags-row">
        <text class="category-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
      </view>
      
      <!-- 第四行：数据统计 -->
      <view class="stats-row">
        <view class="material-stats">
          <text class="stat-item">⬇ {{item.downloadCount}}</text>
          <text class="stat-item">👁 {{item.viewCount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && filesList.length === 0}}">
    <view class="empty-icon">📚</view>
    <view class="empty-text">暂无相关资料</view>
    <view class="empty-tip">试试调整筛选条件</view>
  </view>

  <!-- 到底提示 -->
  <view class="load-more" wx:if="{{!loading && filesList.length > 0 && !hasMore}}">
    <text class="load-more-text">已加载全部资料</text>
  </view>
</view>

<!-- 年级筛选弹窗 -->
<view class="popup-mask" wx:if="{{showGradePopup}}" bindtap="hideGradeFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择年级</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{normalFilters.selectedGrade === item ? 'active' : ''}}" 
              wx:for="{{gradeOptions}}" wx:key="*this" 
              bindtap="selectGrade" data-grade="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 科目筛选弹窗 -->
<view class="popup-mask" wx:if="{{showSubjectPopup}}" bindtap="hideSubjectFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择科目</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{normalFilters.selectedSubject === item ? 'active' : ''}}" 
              wx:for="{{subjectOptions}}" wx:key="*this" 
              bindtap="selectSubject" data-subject="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 册别筛选弹窗 -->
<view class="popup-mask" wx:if="{{showVolumePopup}}" bindtap="hideVolumeFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择册别</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{normalFilters.selectedVolume === item ? 'active' : ''}}" 
              wx:for="{{volumeOptions}}" wx:key="*this" 
              bindtap="selectVolume" data-volume="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 板块筛选弹窗（常规模式） -->
<view class="popup-mask" wx:if="{{showSectionPopup && currentMode === 'normal'}}" bindtap="hideSectionFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择板块</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{normalFilters.selectedSection === item ? 'active' : ''}}" 
              wx:for="{{sectionOptions}}" wx:key="*this" 
              bindtap="selectSection" data-section="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 专区类型筛选弹窗 -->
<view class="popup-mask" wx:if="{{showUpgradeTypePopup}}" bindtap="hideUpgradeTypeFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择专区</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{upgradeFilters.selectedUpgradeType === item ? 'active' : ''}}" 
              wx:for="{{upgradeTypeOptions}}" wx:key="*this" 
              bindtap="selectUpgradeType" data-type="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 专区板块筛选弹窗 -->
<view class="popup-mask" wx:if="{{showUpgradeCategoryPopup}}" bindtap="hideUpgradeCategoryFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择板块</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{upgradeFilters.selectedCategory === item ? 'active' : ''}}" 
              wx:for="{{upgradeCategoryOptions}}" wx:key="*this" 
              bindtap="selectUpgradeCategory" data-category="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>
