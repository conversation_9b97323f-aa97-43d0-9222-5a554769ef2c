         /* 搜索页面样式 - 现代化设计 */

.container {
  height: 100vh;
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 30%, #F8F9FA 30%);
}

/* 搜索头部 - 简洁设计 */
.search-header {
  padding: 60rpx 32rpx 40rpx;
  background: transparent;
}

.search-input-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 24rpx;
  background: white;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.search-input-box:focus-within {
  box-shadow: 0 6rpx 30rpx rgba(22, 119, 255, 0.15);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  color: #999999;
  font-size: 36rpx;
  opacity: 0.6;
  display: flex;
  align-items: center;
  justify-content: center;
}



.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.search-placeholder {
  color: #CCCCCC;
  font-size: 30rpx;
}

.clear-btn {
  padding: 8rpx;
  border-radius: 50%;
  background: #F5F5F5;
  transition: all 0.2s ease;
}

.clear-btn:active {
  background: #E8E8E8;
  transform: scale(0.9);
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
  color: #999999;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}

/* 搜索历史 - 现代卡片设计 */
.search-history {
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  margin: 40rpx 32rpx;
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(22, 119, 255, 0.08);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.history-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: flex;
  align-items: center;
}

.history-title::before {
  content: '🕒';
  margin-right: 16rpx;
  font-size: 32rpx;
}

.clear-history {
  font-size: 28rpx;
  color: #1677FF;
  padding: 12rpx 24rpx;
  background: rgba(22, 119, 255, 0.1);
  border-radius: 24rpx;
  transition: all 0.2s ease;
}

.clear-history:active {
  background: rgba(22, 119, 255, 0.2);
  transform: scale(0.95);
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.history-tag {
  padding: 24rpx 36rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #E8F4FD 100%);
  color: #666666;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 32rpx;
  border: 2rpx solid rgba(22, 119, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: scale(0.8);
}

/* 错位出现动画 */
.history-tag:nth-child(1) { animation-delay: 0.1s; }
.history-tag:nth-child(2) { animation-delay: 0.2s; }
.history-tag:nth-child(3) { animation-delay: 0.3s; }
.history-tag:nth-child(4) { animation-delay: 0.4s; }
.history-tag:nth-child(5) { animation-delay: 0.5s; }
.history-tag:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInScale {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.history-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

/* 涟漪效果 */
.history-tag::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.history-tag:active {
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.15);
}

.history-tag:active::before {
  left: 100%;
}

.history-tag:active::after {
  width: 160rpx;
  height: 160rpx;
}

/* 热门搜索 - 现代卡片设计 */
.hot-search {
  background: linear-gradient(135deg, #FFFFFF 0%, #FFF8F0 100%);
  margin: 40rpx 32rpx;
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(255, 107, 53, 0.08);
  animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(60rpx);
  animation-delay: 0.4s;
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hot-header {
  margin-bottom: 40rpx;
}

.hot-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: flex;
  align-items: center;
}

.hot-title::before {
  content: '🔥';
  margin-right: 16rpx;
  font-size: 32rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.hot-tag {
  padding: 24rpx 36rpx;
  background: linear-gradient(135deg, #FFE7E0 0%, #FFF2EF 100%);
  color: #FF6B35;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 32rpx;
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: scale(0.8);
}

/* 错位出现动画 */
.hot-tag:nth-child(1) { animation-delay: 0.2s; }
.hot-tag:nth-child(2) { animation-delay: 0.3s; }
.hot-tag:nth-child(3) { animation-delay: 0.4s; }
.hot-tag:nth-child(4) { animation-delay: 0.5s; }
.hot-tag:nth-child(5) { animation-delay: 0.6s; }
.hot-tag:nth-child(6) { animation-delay: 0.7s; }

.hot-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

/* 涟漪效果 */
.hot-tag::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.hot-tag:active {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  color: white;
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(255, 107, 53, 0.3);
}

.hot-tag:active::before {
  left: 100%;
}

.hot-tag:active::after {
  width: 160rpx;
  height: 160rpx;
}



/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #f8f9fa;
  color: #1677FF;
}

.suggestion-icon {
  margin-right: 16rpx;
  opacity: 0.6;
}

.suggestion-text {
  flex: 1;
}

/* 搜索结果 */
.search-results {
  flex: 1;
  background: #F8F9FA;
}

.result-header {
  padding: 32rpx;
  background-color: white;
  border-bottom: 1px solid #E8E8E8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-count {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.4;
}

.search-keyword {
  color: #1677FF;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 排序触发器 */
.sort-trigger {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  border: 2rpx solid #D6E4FF;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.sort-trigger:active {
  background: linear-gradient(135deg, #D6E4FF 0%, #E6F4FF 100%);
  transform: scale(0.95);
}

.sort-text {
  font-size: 26rpx;
  color: #1677FF;
  font-weight: 500;
  margin-right: 8rpx;
}

.sort-arrow {
  font-size: 24rpx;
  color: #1677FF;
  font-weight: bold;
}

.result-list {
  background-color: white;
}

.result-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 16rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  transition: background-color 0.2s ease;
}

.result-item:active {
  background-color: #F8F9FA;
}

/* 第一行：标题 */
.title-row {
  display: flex;
  align-items: flex-start;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 第二行：文件信息行 */
.file-info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.file-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.file-pages, .file-size {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.feature-tags-inline {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.feature-tag-small {
  background: linear-gradient(135deg, #fff7f0 0%, #ffe8d6 100%);
  color: #ff6b35;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  border: 1rpx solid #ffd4b3;
  font-weight: 500;
}

/* 第三行：标签 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.category-tag {
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid #D6E4FF;
  font-weight: 500;
}

/* 第四行：统计数据 + 价格 */
.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

.material-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b35;
}

/* 保留原有的其他样式 */
.result-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #F0F0F0;
  color: #666666;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.result-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF6B35;
  align-self: flex-end;
}

.text-function {
  color: #FF6B35;
}

/* 省略号样式 */
.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 加载和空状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 输入状态提示 */
.typing-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #666666;
  font-size: 28rpx;
}

.typing-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.7;
  animation: typing 1s ease-in-out infinite;
}

.typing-text {
  color: #999999;
  font-size: 26rpx;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes typing {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #999999;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #CCCCCC;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999999;
}

.load-more-content {
  color: #999999;
}

.loading .load-more-content {
  color: #1677FF;
}

.no-more .load-more-content {
  color: #CCCCCC;
}

/* 排序工具弹窗 */
.sort-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sort-content {
  width: 100%;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx 32rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.sort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.sort-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.sort-close {
  width: 48rpx;
  height: 48rpx;
  background: #F5F5F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  transition: all 0.2s ease;
}

.sort-close:active {
  background: #E8E8E8;
  transform: scale(0.9);
}

.sort-options {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%);
  border: 2rpx solid #E8E8E8;
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.sort-option.active {
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  border-color: #1677FF;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.15);
}

.sort-option:active {
  transform: scale(0.98);
}

.sort-option-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.sort-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.sort-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.sort-option.active .sort-name {
  color: #1677FF;
  font-weight: 600;
}

.sort-direction {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(22, 119, 255, 0.1);
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.sort-direction:active {
  background: rgba(22, 119, 255, 0.2);
  transform: scale(0.95);
}

.direction-text {
  font-size: 24rpx;
  color: #1677FF;
  font-weight: 500;
}

.direction-arrow {
  font-size: 20rpx;
  color: #1677FF;
  font-weight: bold;
}

/* 非激活状态的排序方向 */
.sort-direction-inactive {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  opacity: 0.6;
}

.direction-text-inactive {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}

.direction-arrow-inactive {
  font-size: 20rpx;
  color: #999999;
  font-weight: normal;
}
